#include "plugin_manager.h"
#include "logger.h"
#include "utils.h"

#ifdef _WIN32
#include <io.h>
#include <direct.h>
#else
#include <dirent.h>
#include <sys/stat.h>
#endif

AgentResult plugin_manager_init(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Initialize plugin array
    memset(ctx->plugins, 0, sizeof(ctx->plugins));
    ctx->plugin_count = 0;
    ctx->last_plugin_scan = 0;
    
    LOG_INFO_MSG("Plugin manager initialized");
    return AGENT_SUCCESS;
}

void plugin_manager_cleanup(AgentContext* ctx)
{
    if (!ctx) {
        return;
    }
    
    // Unload all plugins
    for (int i = 0; i < ctx->plugin_count; i++) {
        if (ctx->plugins[i].state != PLUGIN_STATE_UNLOADED) {
            plugin_manager_unload_plugin(ctx, ctx->plugins[i].name);
        }
    }
    
    ctx->plugin_count = 0;
    LOG_INFO_MSG("Plugin manager cleanup completed");
}

AgentResult plugin_manager_scan_directory(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_DEBUG_MSG("Scanning plugin directory: %s", ctx->config.plugin_directory);
    
#ifdef _WIN32
    char search_pattern[MAX_PATH_LEN];
    snprintf(search_pattern, sizeof(search_pattern), "%s\\*.dll", ctx->config.plugin_directory);
    
    WIN32_FIND_DATAA find_data;
    HANDLE find_handle = FindFirstFileA(search_pattern, &find_data);
    
    if (find_handle == INVALID_HANDLE_VALUE) {
        LOG_WARN_MSG("No plugins found in directory: %s", ctx->config.plugin_directory);
        return AGENT_SUCCESS;
    }
    
    do {
        if (!(find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {
            char plugin_path[MAX_PATH_LEN];
            snprintf(plugin_path, sizeof(plugin_path), "%s\\%s", 
                    ctx->config.plugin_directory, find_data.cFileName);
            
            // Check if plugin is already loaded
            char plugin_name[64];
            strncpy(plugin_name, find_data.cFileName, sizeof(plugin_name) - 1);
            plugin_name[sizeof(plugin_name) - 1] = '\0';
            
            // Remove .dll extension
            char* dot = strrchr(plugin_name, '.');
            if (dot) *dot = '\0';
            
            if (!plugin_manager_find_plugin(ctx, plugin_name)) {
                LOG_INFO_MSG("Found new plugin: %s", plugin_path);
                plugin_manager_load_plugin(ctx, plugin_path);
            }
        }
    } while (FindNextFileA(find_handle, &find_data));
    
    FindClose(find_handle);
#else
    DIR* dir = opendir(ctx->config.plugin_directory);
    if (!dir) {
        LOG_WARN_MSG("Cannot open plugin directory: %s", ctx->config.plugin_directory);
        return AGENT_ERROR_SYSTEM;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) {
        if (strstr(entry->d_name, ".so")) {
            char plugin_path[MAX_PATH_LEN];
            snprintf(plugin_path, sizeof(plugin_path), "%s/%s", 
                    ctx->config.plugin_directory, entry->d_name);
            
            char plugin_name[64];
            strncpy(plugin_name, entry->d_name, sizeof(plugin_name) - 1);
            plugin_name[sizeof(plugin_name) - 1] = '\0';
            
            char* dot = strrchr(plugin_name, '.');
            if (dot) *dot = '\0';
            
            if (!plugin_manager_find_plugin(ctx, plugin_name)) {
                LOG_INFO_MSG("Found new plugin: %s", plugin_path);
                plugin_manager_load_plugin(ctx, plugin_path);
            }
        }
    }
    
    closedir(dir);
#endif
    
    ctx->last_plugin_scan = get_timestamp_ms();
    return AGENT_SUCCESS;
}

AgentResult plugin_manager_load_plugin(AgentContext* ctx, const char* plugin_path)
{
    if (!ctx || !plugin_path || ctx->plugin_count >= MAX_PLUGINS) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Extract plugin name from path
    const char* filename = strrchr(plugin_path, PATH_SEPARATOR[0]);
    if (!filename) {
        filename = plugin_path;
    } else {
        filename++; // Skip separator
    }
    
    PluginInfo* plugin = &ctx->plugins[ctx->plugin_count];
    memset(plugin, 0, sizeof(PluginInfo));
    
    strncpy(plugin->name, filename, sizeof(plugin->name) - 1);
    strncpy(plugin->path, plugin_path, sizeof(plugin->path) - 1);
    
    // Remove extension from name
    char* dot = strrchr(plugin->name, '.');
    if (dot) *dot = '\0';
    
#ifdef _WIN32
    plugin->handle = LoadLibraryA(plugin_path);
    if (!plugin->handle) {
        snprintf(plugin->last_error, sizeof(plugin->last_error), 
                "Failed to load DLL: %lu", GetLastError());
        plugin->state = PLUGIN_STATE_ERROR;
        LOG_ERROR_MSG("Failed to load plugin %s: %s", plugin->name, plugin->last_error);
        return AGENT_ERROR_PLUGIN;
    }
    
    // Load function pointers
    plugin->init_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_init");
    plugin->execute_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_execute");
    plugin->cleanup_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_cleanup");
    plugin->get_data_func = (char*(*)(void))GetProcAddress(plugin->handle, "plugin_get_data");
#else
    plugin->handle = dlopen(plugin_path, RTLD_LAZY);
    if (!plugin->handle) {
        snprintf(plugin->last_error, sizeof(plugin->last_error), 
                "Failed to load SO: %s", dlerror());
        plugin->state = PLUGIN_STATE_ERROR;
        LOG_ERROR_MSG("Failed to load plugin %s: %s", plugin->name, plugin->last_error);
        return AGENT_ERROR_PLUGIN;
    }
    
    plugin->init_func = (int(*)(void))dlsym(plugin->handle, "plugin_init");
    plugin->execute_func = (int(*)(void))dlsym(plugin->handle, "plugin_execute");
    plugin->cleanup_func = (int(*)(void))dlsym(plugin->handle, "plugin_cleanup");
    plugin->get_data_func = (char*(*)(void))dlsym(plugin->handle, "plugin_get_data");
#endif
    
    // Initialize plugin
    if (plugin->init_func) {
        int result = plugin->init_func();
        if (result == 0) {
            plugin->state = PLUGIN_STATE_LOADED;
            plugin->execution_interval = 60000; // Default 60 seconds
            ctx->plugin_count++;
            ctx->stats.plugin_count = ctx->plugin_count;
            
            LOG_INFO_MSG("Plugin loaded successfully: %s", plugin->name);
            return AGENT_SUCCESS;
        } else {
            snprintf(plugin->last_error, sizeof(plugin->last_error), 
                    "Plugin initialization failed: %d", result);
            plugin->state = PLUGIN_STATE_ERROR;
        }
    } else {
        strcpy(plugin->last_error, "Plugin init function not found");
        plugin->state = PLUGIN_STATE_ERROR;
    }
    
    // Cleanup on error
#ifdef _WIN32
    FreeLibrary(plugin->handle);
#else
    dlclose(plugin->handle);
#endif
    
    LOG_ERROR_MSG("Failed to initialize plugin %s: %s", plugin->name, plugin->last_error);
    return AGENT_ERROR_PLUGIN;
}

AgentResult plugin_manager_unload_plugin(AgentContext* ctx, const char* plugin_name)
{
    if (!ctx || !plugin_name) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    PluginInfo* plugin = plugin_manager_find_plugin(ctx, plugin_name);
    if (!plugin) {
        return AGENT_ERROR_NOT_FOUND;
    }
    
    // Cleanup plugin
    if (plugin->cleanup_func && plugin->state == PLUGIN_STATE_LOADED) {
        plugin->cleanup_func();
    }
    
    // Unload library
    if (plugin->handle) {
#ifdef _WIN32
        FreeLibrary(plugin->handle);
#else
        dlclose(plugin->handle);
#endif
    }
    
    plugin->state = PLUGIN_STATE_UNLOADED;
    LOG_INFO_MSG("Plugin unloaded: %s", plugin->name);
    
    return AGENT_SUCCESS;
}

AgentResult plugin_manager_execute_plugins(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    uint64_t now = get_timestamp_ms();
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];
        
        if (plugin->state == PLUGIN_STATE_LOADED &&
            now - plugin->last_execution >= plugin->execution_interval) {
            
            AgentResult result = plugin_execute_single(plugin);
            if (result == AGENT_SUCCESS) {
                plugin->last_execution = now;
            }
        }
    }
    
    return AGENT_SUCCESS;
}

PluginInfo* plugin_manager_find_plugin(AgentContext* ctx, const char* plugin_name)
{
    if (!ctx || !plugin_name) {
        return NULL;
    }
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        if (strcmp(ctx->plugins[i].name, plugin_name) == 0) {
            return &ctx->plugins[i];
        }
    }
    
    return NULL;
}

AgentResult plugin_execute_single(PluginInfo* plugin)
{
    if (!plugin || !plugin->execute_func) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    plugin->state = PLUGIN_STATE_RUNNING;
    
    int result = plugin->execute_func();
    
    if (result == 0) {
        plugin->state = PLUGIN_STATE_LOADED;
        return AGENT_SUCCESS;
    } else {
        snprintf(plugin->last_error, sizeof(plugin->last_error), 
                "Plugin execution failed: %d", result);
        plugin->state = PLUGIN_STATE_ERROR;
        return AGENT_ERROR_PLUGIN;
    }
}

char* plugin_collect_data(AgentContext* ctx)
{
    if (!ctx) {
        return NULL;
    }
    
    char* combined_data = malloc(MAX_MESSAGE_SIZE);
    if (!combined_data) {
        return NULL;
    }
    
    strcpy(combined_data, "{\"plugins\":[");
    bool first = true;
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];
        
        if (plugin->state == PLUGIN_STATE_LOADED && plugin->get_data_func) {
            char* plugin_data = plugin->get_data_func();
            if (plugin_data) {
                if (!first) {
                    strcat(combined_data, ",");
                }
                
                char plugin_entry[512];
                snprintf(plugin_entry, sizeof(plugin_entry),
                        "{\"name\":\"%s\",\"data\":%s}", plugin->name, plugin_data);
                
                strcat(combined_data, plugin_entry);
                first = false;
                
                free(plugin_data);
            }
        }
    }
    
    strcat(combined_data, "]}");
    return combined_data;
}

void plugin_manager_get_status(AgentContext* ctx, char* buffer, size_t buffer_size)
{
    if (!ctx || !buffer) {
        return;
    }
    
    snprintf(buffer, buffer_size,
            "Plugin Manager Status:\n"
            "- Total plugins: %d\n"
            "- Plugin directory: %s\n"
            "- Last scan: %llu ms ago\n",
            ctx->plugin_count,
            ctx->config.plugin_directory,
            get_timestamp_ms() - ctx->last_plugin_scan);
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];
        char status_line[256];
        
        const char* state_str;
        switch (plugin->state) {
            case PLUGIN_STATE_UNLOADED: state_str = "Unloaded"; break;
            case PLUGIN_STATE_LOADED: state_str = "Loaded"; break;
            case PLUGIN_STATE_RUNNING: state_str = "Running"; break;
            case PLUGIN_STATE_ERROR: state_str = "Error"; break;
            default: state_str = "Unknown"; break;
        }
        
        snprintf(status_line, sizeof(status_line),
                "- %s: %s (%s)\n", plugin->name, state_str, plugin->path);
        
        strncat(buffer, status_line, buffer_size - strlen(buffer) - 1);
    }
}